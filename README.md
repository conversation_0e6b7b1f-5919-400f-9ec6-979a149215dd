# [IsotopeAI](https://isotopeai.in)

**IsotopeAI** is the ultimate productivity platform that combines AI assistance, time management, analytics, and collaboration tools to help individuals and teams achieve their goals efficiently. Whether you're a student, professional, entrepreneur, or lifelong learner, IsotopeAI adapts to your workflow and amplifies your productivity.

## 🎯 **Focus. Track. Achieve. Repeat.**

## ✨ Features

- **AI-Powered Assistance**: Get instant help with any questions, research, brainstorming, or problem-solving.
- **Focus & Time Management**: Pomodoro timers, focus sessions, and productivity tracking.
- **Analytics & Insights**: Detailed analytics to understand your productivity patterns and optimize performance.
- **Collaboration Tools**: Create or join groups, share resources, and work together towards common goals.
- **Project & Goal Tracking**: Comprehensive task management with Kanban boards and priority-based organization.
- **24/7 Availability**: Access your productivity tools anytime, anywhere.
- **Personalized Experience**: Adaptive interface that learns from your workflow and preferences.
- **Cross-Platform**: Works seamlessly on desktop, tablet, and mobile devices.
- **Progressive Web App**: Install on your device for offline access and native app experience.

## 🚀 How It Works

1. **Set Your Goals**: Define what you want to achieve and organize your projects and tasks.
2. **Focus & Track**: Use our focus timers and productivity tools to work efficiently.
3. **Collaborate**: Join groups, share resources, and work with others towards common objectives.
4. **Analyze & Optimize**: Review your productivity analytics to understand patterns and improve performance.

## 📚 Perfect For

- **Students**: Manage coursework, track study sessions, and collaborate on projects
- **Professionals**: Organize work tasks, track time, and boost productivity
- **Entrepreneurs**: Manage business goals, track progress, and collaborate with teams
- **Freelancers**: Organize client work, track billable hours, and manage deadlines
- **Teams**: Coordinate projects, share resources, and track collective progress
- **Lifelong Learners**: Set learning goals, track skill development, and connect with like-minded individuals

## 🛠️ Built With

- **React.js**: Frontend framework
- **Tailwind CSS**: Styling
- **Gemini API**: AI model
- **Cloudinary**: Cloud-based image storage and management
- **Firebase**: Backend and authentication
- **TypeScript**: Type safety
- **Netlify**: Hosting and serverless functions

## 🌟 Why Choose IsotopeAI?

- **All-in-One Platform**: Everything you need for productivity in one place
- **AI-Powered**: Leverages advanced AI for intelligent assistance and insights
- **Comprehensive Analytics**: Deep insights into your productivity patterns and performance
- **Flexible & Adaptable**: Works for any type of work, learning, or project
- **Beautiful Design**: Modern, intuitive interface that users love
- **Free Forever**: Access core productivity features without barriers
- **Cross-Platform**: Works seamlessly on desktop, tablet, and mobile
- **Privacy-Focused**: Your data stays secure and private

## Recent Updates

### Inactive User Cleanup
- The application now automatically deletes inactive user accounts and their data after 3 months of inactivity
- Users will receive notification warnings at 2.5 months and 2.75 months of inactivity
- User activity is tracked across the application to ensure accurate inactivity detection
- This helps maintain database performance and reduces storage costs by cleaning up unused accounts

---

## Setup Procedure
- Get API key from: [Google AI Studio](https://aistudio.google.com/app/apikey)
- Get Brevo API key from: [Brevo](https://app.brevo.com/) (for welcome emails)
- Add these keys to your `.env` file
- `npm install`
- `npm run dev`

## Brevo Email Campaign Setup
1. Create a Brevo account at [app.brevo.com](https://app.brevo.com) if you don't have one already
2. Create a new email campaign:
   - Navigate to 'Campaigns' > 'Email' > 'Create an email campaign'
   - Design your welcome email using Brevo's template editor or HTML editor
   - Use personalization variables like `{FIRSTNAME}` and `{REGISTRATION_DATE}` in your template
3. Save the campaign as a template
4. Get the campaign ID (visible in the URL when editing the campaign)
5. Update the campaign ID in `src/utils/emailUtils.ts`
6. Ensure your Brevo API key is set in the `.env` file as `VITE_BREVO_API_KEY`

### Personalizing Your Welcome Emails
The integration automatically adds the following personalization attributes to your Brevo contacts:
- `FIRSTNAME`: The user's display name or first part of their email if no name is available
- `REGISTRATION_DATE`: The date when the user registered in YYYY-MM-DD format

You can use these variables in your Brevo email templates with:
```
Hello {FIRSTNAME},
Thank you for joining us on {REGISTRATION_DATE}!
```

### Managing Contact Lists
To add users to specific contact lists in Brevo:
1. Create lists in Brevo: go to 'Contacts' > 'Lists' > 'Create a list'
2. Get the list ID(s) (visible in the URL when viewing a list)
3. Modify the `sendWelcomeEmail` function in `src/utils/emailUtils.ts` to add users to your lists:
   ```typescript
   await createOrUpdateContact(email, displayName, [2, 3]); // Replace 2, 3 with your list IDs
   ```

## Keywords
JEE preparation, NEET preparation, Physics tutor, Chemistry help, Mathematics solver, PCMB doubts, AI tutor, online doubt solving, competitive exam preparation, educational technology, free doubt solving, PCMB learning platform