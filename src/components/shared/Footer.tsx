import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useRef } from 'react';
import { useInView } from 'framer-motion';
import { FeedbackWidget } from '@/components/FeedbackWidget';

const Footer = () => {
  const footerRef = useRef(null);
  const isFooterInView = useInView(footerRef, { once: true, margin: "-100px" });

  const fadeUpVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 1,
        delay: 0.5 + i * 0.2,
        ease: [0.25, 0.4, 0.25, 1],
      },
    }),
  };

  return (
    <motion.footer
      ref={footerRef}
      variants={fadeUpVariants}
      initial="hidden"
      animate={isFooterInView ? "visible" : "hidden"}
      custom={0}
      className="relative z-10 border-t border-white/[0.08] bg-black/20 backdrop-blur-sm"
    >
      {/* Indian Flag Tricolor */}
      <div className="w-full flex flex-row h-4 relative z-20">
        <div className="w-1/3 h-full" style={{ backgroundColor: "#FF671F" }} /> {/* India Saffron */}
        <div className="w-1/3 h-full" style={{ backgroundColor: "#FFFFFF" }} /> {/* White */}
        <div className="w-1/3 h-full" style={{ backgroundColor: "#046A38" }} /> {/* India Green */}
      </div>

      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 pointer-events-none" />

      {/* Decorative glow elements */}
      <div className="absolute bottom-0 left-1/4 w-64 h-32 bg-violet-500/10 rounded-full blur-3xl opacity-40" />
      <div className="absolute bottom-0 right-1/4 w-64 h-32 bg-rose-500/10 rounded-full blur-3xl opacity-40" />

      <div className="container mx-auto px-4 md:px-6 py-12 md:py-16 relative">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-start">
          {/* Logo and description */}
          <div className="md:col-span-4 flex flex-col space-y-6">
            <div className="flex items-center space-x-3">
              <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-12 h-12 rounded-full border border-white/10 shadow-lg" />
              <span className="font-semibold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-white/90 to-white/70">
                IsotopeAI
              </span>
            </div>

            <p className="text-white/40 max-w-md leading-relaxed">
              The ultimate productivity platform that combines AI assistance, time management, analytics, and collaboration tools to help individuals and teams achieve their goals efficiently.
            </p>

            <div className="text-center">
              <span className="text-sm font-medium text-white/60 tracking-wider">Focus. Track. Achieve. Repeat.</span>
            </div>

            <div className="flex items-center space-x-4 pt-2">
              <a
                href="https://www.instagram.com/isotope.ai/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-instagram"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                  <path d="M17.5 6.5h.01" />
                </svg>
              </a>
              <a
                href="https://www.reddit.com/r/Isotope/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <circle cx="12" cy="9" r="1" />
                  <circle cx="12" cy="15" r="1" />
                  <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                  <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                  <path d="M7.5 13h9" />
                  <path d="M10 16v-3" />
                  <path d="M14 16v-3" />
                </svg>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect width="20" height="16" x="2" y="4" rx="2" />
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                </svg>
              </a>
            </div>
          </div>

          {/* Features column */}
          <div className="md:col-span-2">
            <h3 className="text-white/90 font-semibold mb-4">Features</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/ai-landing" className="text-white/50 hover:text-white/90 transition-colors">AI Assistant</Link>
              </li>
              <li>
                <Link to="/groups-landing" className="text-white/50 hover:text-white/90 transition-colors">Collaboration</Link>
              </li>
              <li>
                <Link to="/productivity-landing" className="text-white/50 hover:text-white/90 transition-colors">Focus & Time Management</Link>
              </li>
              <li>
                <Link to="/tasks-landing" className="text-white/50 hover:text-white/90 transition-colors">Project & Goal Tracking</Link>
              </li>
              <li>
                <Link to="/analytics" className="text-white/50 hover:text-white/90 transition-colors">Analytics</Link>
              </li>
            </ul>
          </div>

          {/* Resources column */}
          <div className="md:col-span-2">
            <h3 className="text-white/90 font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Changelog</a>
              </li>
              <li>
                <FeedbackWidget className="inline text-white/50 hover:text-white/90 transition-colors cursor-pointer" />
              </li>
              <li>
                <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Community</a>
              </li>
            </ul>
          </div>

          {/* Legal column */}
          <div className="md:col-span-2">
            <h3 className="text-white/90 font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/privacy-policy" className="text-white/50 hover:text-white/90 transition-colors">Privacy Policy</Link>
              </li>
              <li>
                <Link to="/terms-of-service" className="text-white/50 hover:text-white/90 transition-colors">Terms of Service</Link>
              </li>
              <li>
                <Link to="/about-us" className="text-white/50 hover:text-white/90 transition-colors">About Us</Link>
              </li>
            </ul>
          </div>

          {/* Contact column */}
          <div className="md:col-span-2">
            <h3 className="text-white/90 font-semibold mb-4">Contact</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/contact-us" className="text-white/50 hover:text-white/90 transition-colors">Contact Us</Link>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="text-white/50 hover:text-white/90 transition-colors">Email Us</a>
              </li>
              <li>
                <a href="https://www.instagram.com/isotope.ai/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Instagram</a>
              </li>
              <li>
                <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Reddit</a>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-6 border-t border-white/[0.08] flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/40 text-sm">
            © {new Date().getFullYear()} IsotopeAI. All rights reserved.
          </p>
          <div className="flex items-center gap-4 mt-2 md:mt-0">
            <Link to="/about-us" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
              About Us
            </Link>
            <span className="text-white/20">|</span>
            <Link to="/privacy-policy" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
              Privacy Policy
            </Link>
            <span className="text-white/20">|</span>
            <Link to="/terms-of-service" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
              Terms of Service
            </Link>
            <span className="text-white/20">|</span>
            <Link to="/ads" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
              Ads
            </Link>
            <p className="text-white/40 text-sm ml-4">
              Built with <span className="text-red-500">❤️</span> for productivity enthusiasts
            </p>
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;