<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-C823RR97DM"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-C823RR97DM');
    </script>
    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9602732057654649"
     crossorigin="anonymous"></script>
    <meta charset="UTF-8" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="google-site-verification" content="kv7PyTeC5eMUIW_qepJUof-eaU2B3OySFljnrqLfr04" />

    <!-- Ad-related meta tags -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="ad-client" content="ca-pub-9602732057654649" />
    <meta name="ad-format" content="auto" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Anta&display=swap" rel="stylesheet">

    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">

    <meta
      name="description"
      content="IsotopeAI is the ultimate productivity platform that combines AI assistance, time management, analytics, and collaboration tools to help individuals and teams achieve their goals efficiently. Focus. Track. Achieve. Repeat."
    />
    <meta
      name="keywords"
      content="IsotopeAI, productivity app, AI productivity assistant, time tracking, focus timer, goal tracking, task management, team collaboration, work analytics, productivity dashboard, habit tracker, AI assistant, project management, workflow optimization"
    />
    <title>IsotopeAI - Ultimate AI-Powered Productivity Platform | Focus. Track. Achieve. Repeat.</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#166534" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="IsotopeAI" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" />

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="author" content="IsotopeAI" />
    <meta name="language" content="English" />
    <link rel="canonical" href="https://isotopeai.in/" />

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="IsotopeAI - AI-Powered Physics, Chemistry & Mathematics Doubt Solver" />
    <meta
      property="og:description"
      content="IsotopeAI is an AI-powered platform designed to solve your PCMB doubts. Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology."
    />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:url" content="https://isotopeai.in/" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="IsotopeAI" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="IsotopeAI - AI-Powered Physics, Chemistry & Mathematics Doubt Solver" />
    <meta name="twitter:description" content="Get instant help with Physics, Chemistry, and Mathematics problems using advanced AI technology." />
    <meta name="twitter:image" content="/og-image.png" />
    <meta name="twitter:url" content="https://isotopeai.in" />

    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "IsotopeAI",
      "description": "IsotopeAI is the ultimate productivity platform that combines AI assistance, time management, analytics, and collaboration tools to help individuals and teams achieve their goals efficiently.",
      "url": "https://isotopeai.in/",
      "applicationCategory": "ProductivityApplication",
      "offers": {
        "@type": "Offer",
        "price": "0"
      },
      "operatingSystem": "Any"
    }
    </script>
    <script async src="https://fundingchoicesmessages.google.com/i/pub-9602732057654649?ers=1" onerror="console.warn('Google Funding Choices failed to load')"></script><script>(function() {function signalGooglefcPresent() {if (!window.frames['googlefcPresent']) {if (document.body) {const iframe = document.createElement('iframe'); iframe.style = 'width: 0; height: 0; border: none; z-index: -1000; left: -1000px; top: -1000px;'; iframe.style.display = 'none'; iframe.name = 'googlefcPresent'; document.body.appendChild(iframe);} else {setTimeout(signalGooglefcPresent, 0);}}}signalGooglefcPresent();})();</script>
  </head>
  <body>
    <div id="root"></div>
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('ServiceWorker registration successful');
            })
            .catch((err) => {
              console.log('ServiceWorker registration failed: ', err);
            });
        });
      }
    </script>
    <!-- Cloudflare Web Analytics -->
     <script defer src='https://static.cloudflareinsights.com/beacon.min.js' data-cf-beacon='{"token": "7a43d7e470764db4a5f24947a91652a6"}' onerror="console.warn('Cloudflare Analytics failed to load')"></script>
     <!-- End Cloudflare Web Analytics -->

    <!-- Featurebase SDK -->
    <script>
      !(function (e, t) {
        const a = "featurebase-sdk";
        function n() {
          if (!t.getElementById(a)) {
            var e = t.createElement("script");
            (e.id = a),
            (e.src = "https://do.featurebase.app/js/sdk.js"),
            t.getElementsByTagName("script")[0].parentNode.insertBefore(
              e,
              t.getElementsByTagName("script")[0]
            );
          }
        }
        "function" != typeof e.Featurebase &&
          (e.Featurebase = function () {
            (e.Featurebase.q = e.Featurebase.q || []).push(arguments);
          }),
        "complete" === t.readyState || "interactive" === t.readyState
          ? n()
          : t.addEventListener("DOMContentLoaded", n);
      })(window, document);
    </script>

    <!-- Featurebase User Identification -->
    <script>
      // Function to identify user in Featurebase
      function identifyFeaturebaseUser(userData) {
        Featurebase(
          "identify",
          {
            organization: "isotopeai", // Replace with your actual organization name
            ...userData,
            // Optional callback function
          },
          (err) => {
            if (err) {
              console.error("Featurebase identification error:", err);
            } else {
              console.log("Featurebase identification successful");
            }
          }
        );
      }

      // Example usage - you should call this when user data is available
      // identifyFeaturebaseUser({
      //   email: "<EMAIL>",
      //   name: "User Name",
      //   userId: "user123"
      // });
    </script>
  </body>
</html>
